// 主应用类
class LanguageMentorApp {
    constructor() {
        this.currentTab = 'scenario';
        this.currentScenario = null;
        this.isInitialized = false;
        
        this.init();
    }

    async init() {
        try {
            // 检查API连接
            await this.checkAPIConnection();
            
            // 初始化事件监听器
            this.initEventListeners();
            
            // 加载设置
            this.loadSettings();

            // 初始化头像功能
            this.initAvatarFeature();

            // 初始化书籍选择功能
            this.initBookSelector();
        
            // 初始化案例检索功能
            this.initCaseSearchFeature();

            // 初始化律师推荐功能
            this.initLawyerRecommendationFeature();

            // 初始化文书生成功能
            this.initDocumentGenerationFeature();

            // 加载律师推荐介绍
            await this.loadLawyerRecommendationIntro();
            
            this.isInitialized = true;
            console.log('LanguageMentor 应用初始化完成');
            
        } catch (error) {
            console.error('应用初始化失败:', error);
            ErrorHandler.show('应用初始化失败，请检查后端服务是否正常运行');
        }
    }

    async checkAPIConnection() {
        try {
            await api.healthCheck();
            console.log('API 连接正常');
        } catch (error) {
            throw new Error('无法连接到后端服务');
        }
    }

    initEventListeners() {
        // 导航项切换
        document.querySelectorAll('.nav-item').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const tabId = e.currentTarget.dataset.tab;
                this.switchTab(tabId);
            });
        });

        // 场景选择
        document.querySelectorAll('input[name="scenario"]').forEach(radio => {
            radio.addEventListener('change', (e) => {
                this.handleScenarioChange(e.target.value);
            });
        });

        // 聊天输入框回车发送
        this.initChatInputs();

        // 设置相关
        this.initSettingsModal();

    }

    initChatInputs() {
        // 场景聊天
        const scenarioInput = document.getElementById('scenarioInput');
        const scenarioSendBtn = document.getElementById('scenarioSendBtn');
        
        if (scenarioInput && scenarioSendBtn) {
            scenarioInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter' && !scenarioSendBtn.disabled) {
                    this.sendScenarioMessage();
                }
            });
            
            scenarioSendBtn.addEventListener('click', () => {
                this.sendScenarioMessage();
            });
        }

        // 自由对话
        const conversationInput = document.getElementById('conversationInput');
        const conversationSendBtn = document.getElementById('conversationSendBtn');

        if (conversationInput && conversationSendBtn) {
            conversationInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.sendConversationMessage();
                }
            });

            conversationSendBtn.addEventListener('click', () => {
                this.sendConversationMessage();
            });
        }

        // 初始化语音功能
        this.initVoiceFeatures();

        // 词汇学习
        const vocabInput = document.getElementById('vocabInput');
        const vocabSendBtn = document.getElementById('vocabSendBtn');
        
        if (vocabInput && vocabSendBtn) {
            vocabInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.sendVocabMessage();
                }
            });
            
            vocabSendBtn.addEventListener('click', () => {
                this.sendVocabMessage();
            });
        }

        // 案例检索
        const caseSearchInput = document.getElementById('caseSearchInput');
        const caseSearchSendBtn = document.getElementById('caseSearchSendBtn');

        if (caseSearchInput && caseSearchSendBtn) {
            caseSearchInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.sendCaseSearchMessage();
                }
            });

            caseSearchSendBtn.addEventListener('click', () => {
                this.sendCaseSearchMessage();
            });
        }

        // 文书生成
        const documentGenerationInput = document.getElementById('documentGenerationInput');
        const documentGenerationSendBtn = document.getElementById('documentGenerationSendBtn');

        if (documentGenerationInput && documentGenerationSendBtn) {
            documentGenerationInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.sendDocumentGenerationMessage();
                }
            });

            documentGenerationSendBtn.addEventListener('click', () => {
                this.sendDocumentGenerationMessage();
            });
        }
    }

    initSettingsModal() {
        const settingsBtn = document.getElementById('settingsBtn');
        const closeSettingsBtn = document.getElementById('closeSettingsBtn');
        const cancelSettingsBtn = document.getElementById('cancelSettingsBtn');
        const saveSettingsBtn = document.getElementById('saveSettingsBtn');
        const settingsModal = document.getElementById('settingsModal');

        // 用于保存打开弹窗时的原始状态
        let originalFormState = {};

        if (settingsBtn) {
            settingsBtn.addEventListener('click', () => {
                // 保存当前表单状态
                originalFormState = this.saveCurrentFormState();

                UIComponents.applySettingsToForm();
                UIComponents.showModal('settingsModal');
            });
        }

        if (closeSettingsBtn) {
            closeSettingsBtn.addEventListener('click', () => {
                // 恢复原始状态
                this.restoreFormState(originalFormState);
                UIComponents.hideModal('settingsModal');
            });
        }

        if (cancelSettingsBtn) {
            cancelSettingsBtn.addEventListener('click', () => {
                // 恢复原始状态
                this.restoreFormState(originalFormState);
                UIComponents.hideModal('settingsModal');
            });
        }

        if (saveSettingsBtn) {
            saveSettingsBtn.addEventListener('click', () => {
                saveSettings();
            });
        }

        // 点击模态框外部关闭
        if (settingsModal) {
            settingsModal.addEventListener('click', (e) => {
                if (e.target === settingsModal) {
                    // 恢复原始状态
                    this.restoreFormState(originalFormState);
                    UIComponents.hideModal('settingsModal');
                }
            });
        }
    }

    // 保存当前表单状态
    saveCurrentFormState() {
        const apiKeyInput = document.getElementById('apiKey');
        const currentUserAvatar = document.getElementById('currentUserAvatar');

        return {
            apiKey: apiKeyInput ? apiKeyInput.value : '',
            userAvatar: currentUserAvatar ? currentUserAvatar.src : 'images/user.png',
            userAvatarData: localStorage.getItem('userAvatar') // 保存localStorage中的头像数据
        };
    }

    // 恢复表单状态
    restoreFormState(state) {
        if (!state) return;

        const apiKeyInput = document.getElementById('apiKey');
        const currentUserAvatar = document.getElementById('currentUserAvatar');

        if (apiKeyInput) {
            apiKeyInput.value = state.apiKey || '';
        }

        if (currentUserAvatar && state.userAvatar) {
            currentUserAvatar.src = state.userAvatar;
        }

        // 恢复localStorage中的头像数据
        if (state.userAvatarData !== undefined) {
            if (state.userAvatarData) {
                localStorage.setItem('userAvatar', state.userAvatarData);
            } else {
                localStorage.removeItem('userAvatar');
            }
        }
    }

    switchTab(tabId) {
        this.currentTab = tabId;
        UIComponents.switchTab(tabId);
        
        // 根据标签页执行特定初始化
        if (tabId === 'scenario') {
            // 场景标签页无需特殊处理
        } else if (tabId === 'conversation') {
            // 自由对话标签页
            UIComponents.setInputState('conversationInput', 'conversationSendBtn', true);
        } else if (tabId === 'vocab') {
            // 词汇学习标签页
            UIComponents.setInputState('vocabInput', 'vocabSendBtn', true);
        } else if (tabId === 'lawyer_recommendation') {
            // 律师推荐标签页
            UIComponents.setInputState('lawyerRecommendationInput', 'lawyerRecommendationSendBtn', true);
        }
    }

    async handleScenarioChange(scenarioId) {
        try {
            this.currentScenario = scenarioId;
            LoadingManager.show('加载场景信息...');

            // 显示聊天容器，隐藏场景介绍
            const chatContainer = document.getElementById('scenarioChat');
            const scenarioIntro = document.getElementById('scenarioIntro');

            if (chatContainer) {
                chatContainer.style.display = 'flex';
            }
            if (scenarioIntro) {
                scenarioIntro.style.display = 'none';
            }

            // 获取场景介绍
            const introResponse = await api.getScenarioIntro(scenarioId);
            if (introResponse.success) {
                UIComponents.updateScenarioIntro(introResponse.data.intro);
            }

            // 开始场景对话
            const sessionId = sessionManager.getSessionId('scenario', scenarioId);
            const startResponse = await api.startScenario(scenarioId, sessionId);

            if (startResponse.success) {
                // 清空聊天记录并显示初始消息
                UIComponents.clearMessages('scenarioMessages');
                UIComponents.addMessage('scenarioMessages', startResponse.data.message, false);

                // 启用输入框
                UIComponents.setInputState('scenarioInput', 'scenarioSendBtn', true);
            }

        } catch (error) {
            console.error('场景切换失败:', error);
            ErrorHandler.show('场景加载失败: ' + error.message);
        } finally {
            LoadingManager.hide();
        }
    }

    async sendScenarioMessage() {
        const input = document.getElementById('scenarioInput');
        const message = input.value.trim();
        
        if (!message || !this.currentScenario) return;

        try {
            // 添加用户消息
            UIComponents.addMessage('scenarioMessages', message, true);
            input.value = '';
            
            // 禁用输入框
            UIComponents.setInputState('scenarioInput', 'scenarioSendBtn', false);
            
            // 发送消息到API
            const sessionId = sessionManager.getSessionId('scenario', this.currentScenario);
            const response = await api.scenarioChat(this.currentScenario, message, sessionId);
            
            if (response.success) {
                // 添加机器人回复
                await UIComponents.typeMessage('scenarioMessages', response.data.message, false, 30);
            }
            
        } catch (error) {
            console.error('发送场景消息失败:', error);
            ErrorHandler.show('发送消息失败: ' + error.message);
        } finally {
            // 重新启用输入框
            UIComponents.setInputState('scenarioInput', 'scenarioSendBtn', true);
        }
    }

    async sendConversationMessage() {
        const input = document.getElementById('conversationInput');
        const message = input.value.trim();

        if (!message) return;

        try {
            // 添加用户消息并显示加载动画
            const { messageElement, loadingElement } = UIComponents.addUserMessageWithLoading('conversationMessages', message);
            input.value = '';

            // 禁用输入框
            UIComponents.setInputState('conversationInput', 'conversationSendBtn', false);

            // 移除用户加载动画并显示机器人打字指示器
            UIComponents.removeUserLoadingIndicator('conversationMessages');
            UIComponents.showBotTypingIndicator('conversationMessages');

            // 发送消息到API
            const sessionId = sessionManager.getSessionId('conversation');
            const response = await api.conversationChat(message, sessionId);

            // 隐藏机器人打字指示器
            UIComponents.hideBotTypingIndicator('conversationMessages');

            if (response.success) {
                // 检查不同的响应数据结构
                let responseText = null;
                if (response.data.message) {
                    responseText = response.data.message;
                } else if (response.data.response) {
                    responseText = response.data.response;
                } else if (response.data.text_response) {
                    responseText = response.data.text_response;
                } else if (typeof response.data === 'string') {
                    responseText = response.data;
                }

                // 添加机器人回复
                if (responseText) {
                    await UIComponents.typeMessage('conversationMessages', responseText, false, 30);
                } else {
                    console.error('未找到有效的回复内容:', response.data);
                    UIComponents.addMessage('conversationMessages', '抱歉，我暂时无法回复您的问题。', false);
                }
            }

        } catch (error) {
            console.error('发送对话消息失败:', error);
            // 移除用户加载动画和机器人打字指示器
            UIComponents.removeUserLoadingIndicator('conversationMessages');
            UIComponents.hideBotTypingIndicator('conversationMessages');
            ErrorHandler.show('发送消息失败: ' + error.message);
        } finally {
            // 重新启用输入框
            UIComponents.setInputState('conversationInput', 'conversationSendBtn', true);
        }
    }

    async sendVocabMessage(customMessage = null) {
        const input = document.getElementById('vocabInput');
        const message = customMessage || input.value.trim();

        if (!message) return;

        try {
            // 只有当不是自定义消息时才添加用户消息到界面
            if (!customMessage) {
                UIComponents.addMessage('vocabMessages', message, true);
                input.value = '';
            }

            // 禁用输入框
            UIComponents.setInputState('vocabInput', 'vocabSendBtn', false);

            // 发送消息到API
            const sessionId = sessionManager.getSessionId('vocab');
            const response = await api.vocabChat(message, sessionId);

            if (response.success) {
                // 添加机器人回复
                await UIComponents.typeMessage('vocabMessages', response.data.message, false, 30);
            }

        } catch (error) {
            console.error('发送词汇消息失败:', error);
            ErrorHandler.show('发送消息失败: ' + error.message);
        } finally {
            // 重新启用输入框
            UIComponents.setInputState('vocabInput', 'vocabSendBtn', true);
        }
    }

    async sendCaseSearchMessage() {
        const caseSearchInput = document.getElementById('caseSearchInput');
        const caseSearchSendBtn = document.getElementById('caseSearchSendBtn');
        const caseSearchMessages = document.getElementById('caseSearchMessages');

        const message = caseSearchInput.value.trim();
        if (!message) return;

        try {
            // 禁用输入框
            UIComponents.setInputState('caseSearchInput', 'caseSearchSendBtn', false);

            // 添加用户消息
            UIComponents.addMessage('caseSearchMessages', message, true);

            // 清空输入框
            caseSearchInput.value = '';

            // 显示加载状态
            LoadingManager.show('正在检索相关案例...');

            // 获取会话ID
            const sessionId = sessionManager.getSessionId('case_search');

            // 调用API
            const response = await api.caseSearchChat(message, sessionId);

            if (response.success) {
                // 显示智能体回复
                await UIComponents.typeMessage('caseSearchMessages', response.data.message, false, 30);
            } else {
                throw new Error(response.error || '案例检索失败');
            }

        } catch (error) {
            console.error('案例检索失败:', error);
            ErrorHandler.show('案例检索失败: ' + error.message);
        } finally {
            // 重新启用输入框
            UIComponents.setInputState('caseSearchInput', 'caseSearchSendBtn', true);
            LoadingManager.hide();
        }
    }

    async loadLawyerRecommendationIntro() {
        try {
            const response = await api.getLawyerRecommendationIntro();
            if (response.success) {
                UIComponents.updateLawyerRecommendationIntro(response.data.intro);
            }
        } catch (error) {
            console.error('加载律师推荐介绍失败:', error);
        }
    }

    initLawyerRecommendationFeature() {
        const lawyerRecommendationInput = document.getElementById('lawyerRecommendationInput');
        const lawyerRecommendationSendBtn = document.getElementById('lawyerRecommendationSendBtn');

        if (lawyerRecommendationInput && lawyerRecommendationSendBtn) {
            // 输入框回车事件
            lawyerRecommendationInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    this.sendLawyerRecommendationMessage();
                }
            });

            // 发送按钮点击事件
            lawyerRecommendationSendBtn.addEventListener('click', () => {
                this.sendLawyerRecommendationMessage();
            });
        }
    }

    async sendLawyerRecommendationMessage() {
        const input = document.getElementById('lawyerRecommendationInput');
        const message = input.value.trim();

        if (!message) return;

        console.log('发送律师推荐消息:', message);

        try {
            // 禁用输入
            UIComponents.setInputState('lawyerRecommendationInput', 'lawyerRecommendationSendBtn', false);

            // 添加用户消息
            UIComponents.addMessage('lawyerRecommendationMessages', message, true);

            // 清空输入框
            input.value = '';

            // 显示打字指示器
            UIComponents.showTypingIndicator('lawyerRecommendationTyping');

            // 获取会话ID
            const sessionId = sessionManager.getSessionId('lawyer_recommendation');
            console.log('会话ID:', sessionId);

            // 发送消息到后端
            console.log('发送API请求...');
            const response = await api.lawyerRecommendationChat(message, sessionId);
            console.log('API响应:', response);

            if (response.success) {
                // 添加机器人回复
                console.log('机器人回复内容:', response.data.message);
                console.log('回复内容长度:', response.data.message ? response.data.message.length : 0);

                if (response.data.message) {
                    // 显示智能体回复
                    await UIComponents.typeMessage('lawyerRecommendationMessages', response.data.message, false, 30);
                    console.log('添加机器人回复成功');
                } else {
                    console.error('机器人回复内容为空');
                    await UIComponents.typeMessage('lawyerRecommendationMessages', '抱歉，系统暂时无法处理您的请求，请稍后再试。', false, 30);
                }
            } else {
                throw new Error(response.error || '律师推荐失败');
            }

        } catch (error) {
            console.error('律师推荐对话失败:', error);
            ErrorHandler.show('律师推荐失败: ' + error.message);
        } finally {
            // 隐藏打字指示器
            UIComponents.hideTypingIndicator('lawyerRecommendationTyping');
            // 重新启用输入
            UIComponents.setInputState('lawyerRecommendationInput', 'lawyerRecommendationSendBtn', true);
        }
    }

    loadSettings() {
        UIComponents.applySettingsToForm();

        // 加载用户头像
        const userAvatar = localStorage.getItem('userAvatar');
        if (userAvatar) {
            const currentUserAvatar = document.getElementById('currentUserAvatar');
            if (currentUserAvatar) {
                currentUserAvatar.src = userAvatar;
            }
        }
    }

    initAvatarFeature() {
        const userAvatarInput = document.getElementById('userAvatarInput');
        const currentUserAvatar = document.getElementById('currentUserAvatar');

        if (userAvatarInput && currentUserAvatar) {
            userAvatarInput.addEventListener('change', (event) => {
                const file = event.target.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = (e) => {
                        const avatarData = e.target.result;
                        currentUserAvatar.src = avatarData;
                        localStorage.setItem('userAvatar', avatarData);
                    };
                    reader.readAsDataURL(file);
                }
            });
        }
    }

    initBookSelector() {
        const bookOptions = document.querySelectorAll('input[name="book"]');
        const startLearningBtn = document.getElementById('startLearningBtn');
        const bookSelector = document.querySelector('.book-selector');
        const vocabChat = document.getElementById('vocabChat');

        // 监听书籍选择
        bookOptions.forEach(option => {
            option.addEventListener('change', () => {
                if (option.checked) {
                    startLearningBtn.disabled = false;
                }
            });
        });

        // 开始学习按钮点击事件
        if (startLearningBtn) {
            startLearningBtn.addEventListener('click', () => {
                const selectedBook = document.querySelector('input[name="book"]:checked');
                if (selectedBook) {
                    this.startBookLearning(selectedBook.value);
                    bookSelector.style.display = 'none';
                    vocabChat.style.display = 'flex';
                }
            });
        }
    }

    initCaseSearchFeature() {
        // 为罪名标签添加点击事件
        const crimeTags = document.querySelectorAll('.crime-tag');
        crimeTags.forEach(tag => {
            tag.addEventListener('click', () => {
                const caseSearchInput = document.getElementById('caseSearchInput');
                if (caseSearchInput) {
                    caseSearchInput.value = tag.textContent + '案例';
                    caseSearchInput.focus();
                }
            });
        });
    }

    async startBookLearning(bookType) {
        const bookNames = {
            'constitution': '宪法',
            'criminal_law': '刑法',
            'civil_code': '民法典'
        };

        const bookName = bookNames[bookType] || '法律书籍';

        // 清空聊天记录
        const vocabMessages = document.getElementById('vocabMessages');
        vocabMessages.innerHTML = '';

        // 添加重新开始按钮
        this.addRestartButton();

        try {
            // 显示加载状态
            LoadingManager.show('正在为您制定学习计划...');

            // 重置会话并设置书籍记忆
            sessionManager.resetSession('vocab');
            const sessionId = sessionManager.getSessionId('vocab');

            // 调用API开始学习，传入书籍类型
            const response = await api.startVocab(sessionId, bookType);

            if (response.success) {
                // 显示智能体的回复
                await UIComponents.typeMessage('vocabMessages', response.data.bot_message, false, 30);

                // 启用输入框让用户可以回复
                UIComponents.setInputState('vocabInput', 'vocabSendBtn', true);
            } else {
                throw new Error(response.error || '启动学习失败');
            }

        } catch (error) {
            console.error('启动书籍学习失败:', error);
            ErrorHandler.show('启动学习失败: ' + error.message);
        } finally {
            LoadingManager.hide();
        }
    }

    addRestartButton() {
        const vocabChat = document.getElementById('vocabChat');
        const existingBtn = vocabChat.querySelector('.restart-learning-btn');
        if (!existingBtn) {
            const restartBtn = document.createElement('button');
            restartBtn.className = 'btn btn-outline restart-learning-btn';
            restartBtn.innerHTML = '<i class="fas fa-redo"></i> 重新选择书籍';
            restartBtn.style.margin = '1rem';
            restartBtn.onclick = () => this.restartBookSelection();

            vocabChat.insertBefore(restartBtn, vocabChat.firstChild);
        }
    }

    async restartBookSelection() {
        try {
            // 显示加载状态
            LoadingManager.show('正在重置学习状态...');

            // 重置书籍记忆
            const sessionId = sessionManager.getSessionId('vocab');
            await api.resetBookMemory(sessionId);

            // 重置会话
            sessionManager.resetSession('vocab');

            const bookSelector = document.querySelector('.book-selector');
            const vocabChat = document.getElementById('vocabChat');
            const startLearningBtn = document.getElementById('startLearningBtn');

            // 重置选择状态
            const bookOptions = document.querySelectorAll('input[name="book"]');
            bookOptions.forEach(option => option.checked = false);
            startLearningBtn.disabled = true;

            // 显示书籍选择器，隐藏聊天界面
            bookSelector.style.display = 'block';
            vocabChat.style.display = 'none';

            // 清空聊天记录
            UIComponents.clearMessages('vocabMessages');

        } catch (error) {
            console.error('重置书籍选择失败:', error);
            ErrorHandler.show('重置失败: ' + error.message);
        } finally {
            LoadingManager.hide();
        }
    }

    // 文书生成相关方法
    initDocumentGenerationFeature() {
        // 加载模板列表
        this.loadDocumentTemplates();

        // 初始化模板选择事件
        this.initTemplateSelection();

        // 初始化更换模板按钮
        const changeTemplateBtn = document.getElementById('changeTemplateBtn');
        if (changeTemplateBtn) {
            changeTemplateBtn.addEventListener('click', () => {
                this.showTemplateSelector();
            });
        }
    }

    async loadDocumentTemplates() {
        try {
            const response = await api.getDocumentTemplates();
            if (response.success) {
                this.renderTemplates(response.data);
            } else {
                console.error('获取模板列表失败:', response.error);
            }
        } catch (error) {
            console.error('加载模板列表失败:', error);
        }
    }

    renderTemplates(templates) {
        const categories = {
            '民事诉讼': document.getElementById('civilTemplates'),
            '婚姻家庭': document.getElementById('familyTemplates'),
            '劳动争议': document.getElementById('laborTemplates')
        };

        // 清空现有模板
        Object.values(categories).forEach(container => {
            if (container) container.innerHTML = '';
        });

        // 渲染模板卡片
        templates.forEach(template => {
            const container = categories[template.category];
            if (container) {
                const templateCard = this.createTemplateCard(template);
                container.appendChild(templateCard);
            }
        });
    }

    createTemplateCard(template) {
        const card = document.createElement('div');
        card.className = 'template-card';
        card.dataset.templateId = template.id;

        card.innerHTML = `
            <div class="template-icon">
                <i class="fas fa-file-alt"></i>
            </div>
            <h5>${template.name}</h5>
            <p>${template.description}</p>
            <button class="btn btn-primary btn-sm select-template-btn">
                <i class="fas fa-plus"></i>
                选择模板
            </button>
        `;

        return card;
    }

    initTemplateSelection() {
        document.addEventListener('click', (e) => {
            if (e.target.closest('.select-template-btn')) {
                const templateCard = e.target.closest('.template-card');
                const templateId = templateCard.dataset.templateId;
                this.selectTemplate(templateId);
            }
        });
    }

    async selectTemplate(templateId) {
        try {
            LoadingManager.show('正在初始化文书生成...');

            const response = await api.startDocumentGeneration(templateId);
            if (response.success) {
                // 显示聊天界面
                this.showDocumentGenerationChat();

                // 更新选中的模板信息
                this.updateSelectedTemplateInfo(templateId);

                // 添加初始消息
                UIComponents.addMessage('documentGenerationMessages', response.data.message, false);

                // 保存会话ID
                this.documentGenerationSessionId = response.data.session_id;

            } else {
                ErrorHandler.show('启动文书生成失败: ' + response.error);
            }
        } catch (error) {
            console.error('选择模板失败:', error);
            ErrorHandler.show('选择模板失败: ' + error.message);
        } finally {
            LoadingManager.hide();
        }
    }

    showDocumentGenerationChat() {
        const templateSelector = document.getElementById('templateSelector');
        const documentGenerationChat = document.getElementById('documentGenerationChat');

        if (templateSelector) templateSelector.style.display = 'none';
        if (documentGenerationChat) documentGenerationChat.style.display = 'block';
    }

    showTemplateSelector() {
        const templateSelector = document.getElementById('templateSelector');
        const documentGenerationChat = document.getElementById('documentGenerationChat');

        if (templateSelector) templateSelector.style.display = 'block';
        if (documentGenerationChat) documentGenerationChat.style.display = 'none';

        // 清空聊天记录
        UIComponents.clearMessages('documentGenerationMessages');

        // 重置会话ID
        this.documentGenerationSessionId = null;
    }

    updateSelectedTemplateInfo(templateId) {
        // 这里可以根据templateId更新显示的模板名称
        const selectedTemplateName = document.getElementById('selectedTemplateName');
        if (selectedTemplateName) {
            // 从模板卡片中获取名称
            const templateCard = document.querySelector(`[data-template-id="${templateId}"]`);
            if (templateCard) {
                const templateName = templateCard.querySelector('h5').textContent;
                selectedTemplateName.textContent = templateName;
            }
        }
    }

    async sendDocumentGenerationMessage() {
        const input = document.getElementById('documentGenerationInput');
        const sendBtn = document.getElementById('documentGenerationSendBtn');

        if (!input || !sendBtn) return;

        const message = input.value.trim();
        if (!message) return;

        if (!this.documentGenerationSessionId) {
            ErrorHandler.show('请先选择文书模板');
            return;
        }

        try {
            // 禁用输入
            input.disabled = true;
            sendBtn.disabled = true;

            // 添加用户消息
            UIComponents.addMessage('documentGenerationMessages', message, true);

            // 清空输入框
            input.value = '';

            // 显示打字指示器
            UIComponents.showTypingIndicator('documentGenerationTyping');

            // 发送消息
            const response = await api.sendDocumentGenerationMessage(message, this.documentGenerationSessionId);

            if (response.success) {
                const data = response.data;

                // 检查是否是文档生成结果
                if (data.filename) {
                    // 显示生成成功消息
                    UIComponents.addMessage('documentGenerationMessages', data.message, false);

                    // 添加下载按钮
                    this.addDownloadButton(data.filename);
                } else {
                    // 普通对话消息
                    UIComponents.addMessage('documentGenerationMessages', data.message, false);
                }
            } else {
                ErrorHandler.show('发送消息失败: ' + response.error);
            }

        } catch (error) {
            console.error('发送文书生成消息失败:', error);
            ErrorHandler.show('发送消息失败: ' + error.message);
        } finally {
            // 恢复输入
            input.disabled = false;
            sendBtn.disabled = false;
            input.focus();

            // 隐藏打字指示器
            UIComponents.hideTypingIndicator('documentGenerationTyping');
        }
    }

    addDownloadButton(filename) {
        const messagesContainer = document.getElementById('documentGenerationMessages');
        if (!messagesContainer) return;

        const time = new Date().toLocaleTimeString('zh-CN', {
            hour: '2-digit',
            minute: '2-digit'
        });

        const downloadContainer = document.createElement('div');
        downloadContainer.className = 'message bot download-message';

        downloadContainer.innerHTML = `
            <div class="message-avatar bot-avatar" title="法律助手">
                <img src="images/robot.png" alt="法律助手" width="40" height="40">
            </div>
            <div class="message-wrapper">
                <div class="message-content">
                    <div class="download-section">
                        <div class="download-info">
                            <i class="fas fa-file-word"></i>
                            <span>文书已生成完成</span>
                        </div>
                        <button class="btn btn-primary download-btn" onclick="app.downloadDocument('${filename}')">
                            <i class="fas fa-download"></i>
                            下载 Word 文档
                        </button>
                    </div>
                    <div class="message-time">${time}</div>
                </div>
            </div>
        `;

        messagesContainer.appendChild(downloadContainer);
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }

    async downloadDocument(filename) {
        try {
            console.log('开始下载文档:', filename);
            const downloadUrl = `http://localhost:5000/api/document_generation/download/${encodeURIComponent(filename)}`;
            console.log('下载URL:', downloadUrl);

            // 先检查文件是否存在
            const checkResponse = await fetch(downloadUrl, { method: 'HEAD' });
            if (!checkResponse.ok) {
                throw new Error(`文件不存在或无法访问: ${checkResponse.status}`);
            }

            // 创建隐藏的下载链接
            const link = document.createElement('a');
            link.href = downloadUrl;
            link.download = filename;
            link.style.display = 'none';

            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            ErrorHandler.showSuccess('文档下载已开始');

        } catch (error) {
            console.error('下载文档失败:', error);
            ErrorHandler.show('下载失败: ' + error.message);
        }
    }

    // 初始化语音功能
    initVoiceFeatures() {
        // 为法律问答模块添加语音按钮
        const voiceBtn = VoiceUI.createVoiceButton('conversationChat');
        if (voiceBtn) {
            this.setupVoiceButton(voiceBtn, 'conversation');
        }
    }

    // 设置语音按钮功能
    setupVoiceButton(button, moduleType) {
        let isRecording = false;
        let recordingTimer = null;
        let recordingStartTime = null;
        let silenceTimer = null;

        // 显示圆形"正在聆听"弹窗
        const showListeningModal = () => {
            const content = `
                <div class="listening-content" style="
                    width: 200px;
                    height: 200px;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    border-radius: 50%;
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    text-align: center;
                    box-shadow: 0 20px 40px rgba(102, 126, 234, 0.3);
                ">
                    <div class="listening-icon" style="
                        font-size: 2.5rem;
                        margin-bottom: 1rem;
                        animation: pulse 2s infinite;
                    ">
                        <i class="fas fa-microphone"></i>
                    </div>
                    <div class="listening-text" style="
                        font-size: 1.2rem;
                        font-weight: 600;
                        margin-bottom: 0.5rem;
                    ">正在聆听</div>
                    <div class="listening-timer" id="listeningTimer" style="
                        font-size: 1rem;
                        opacity: 0.8;
                        margin-bottom: 1rem;
                    ">00:00</div>
                    <div class="listening-waves" style="
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        gap: 4px;
                    ">
                        <div class="wave" style="
                            width: 3px;
                            height: 15px;
                            background: rgba(255, 255, 255, 0.8);
                            border-radius: 2px;
                            animation: waveAnimation 1.5s ease-in-out infinite;
                        "></div>
                        <div class="wave" style="
                            width: 3px;
                            height: 15px;
                            background: rgba(255, 255, 255, 0.8);
                            border-radius: 2px;
                            animation: waveAnimation 1.5s ease-in-out infinite;
                            animation-delay: 0.2s;
                        "></div>
                        <div class="wave" style="
                            width: 3px;
                            height: 15px;
                            background: rgba(255, 255, 255, 0.8);
                            border-radius: 2px;
                            animation: waveAnimation 1.5s ease-in-out infinite;
                            animation-delay: 0.4s;
                        "></div>
                    </div>
                </div>
            `;

            // 使用统一的弹窗管理器
            ModalPositionManager.createModal('listeningModal', content, 'conversationChat');

            // 开始计时器
            recordingStartTime = Date.now();
            recordingTimer = setInterval(() => {
                const elapsed = Math.floor((Date.now() - recordingStartTime) / 1000);
                const minutes = Math.floor(elapsed / 60);
                const seconds = elapsed % 60;
                const timer = document.getElementById('listeningTimer');
                if (timer) {
                    timer.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
                }
            }, 1000);
        };

        // 隐藏聆听弹窗
        const hideListeningModal = () => {
            ModalPositionManager.removeModal('listeningModal');
            if (recordingTimer) {
                clearInterval(recordingTimer);
                recordingTimer = null;
            }
            if (silenceTimer) {
                clearTimeout(silenceTimer);
                silenceTimer = null;
            }
        };

        // 分段录音和识别相关变量
        let isSegmentRecording = false;
        let segmentTimer = null;
        let accumulatedText = '';
        let segmentCount = 0;
        let currentSegmentTimeout = null; // 当前录音段的超时器
        let pendingRecognitions = new Map(); // 待处理的识别任务
        let lastEmptySegmentId = 0; // 最后一个空段的ID

        // 开始异步分段录音循环（每10秒一段）
        const startSegmentedRecording = () => {
            if (!isSegmentRecording) return;

            console.log('=== 开始异步分段录音 ===');

            // 立即开始第一段录音
            startNextSegmentAsync();
        };

        // 异步开始下一段录音
        const startNextSegmentAsync = () => {
            if (!isSegmentRecording) return;

            segmentCount++;
            const currentSegmentId = segmentCount;
            console.log(`开始第${currentSegmentId}段录音（异步）`);

            // 异步开始录音，不等待识别完成
            startSingleSegmentRecording()
                .then(audioData => {
                    if (audioData && isSegmentRecording) {
                        console.log(`第${currentSegmentId}段录音完成，开始异步识别`);

                        // 异步处理识别，不阻塞下一段录音
                        processSegmentAsync(audioData, currentSegmentId);

                        // 立即开始下一段录音（不等待识别完成）
                        setTimeout(() => {
                            if (isSegmentRecording) {
                                startNextSegmentAsync();
                            }
                        }, 100); // 短暂延迟后开始下一段

                    } else if (!audioData && isSegmentRecording) {
                        // 录音失败但仍在录音状态，重试
                        console.log(`第${currentSegmentId}段录音失败，重试`);
                        setTimeout(() => {
                            if (isSegmentRecording) {
                                startNextSegmentAsync();
                            }
                        }, 500);
                    }
                })
                .catch(error => {
                    console.error(`第${currentSegmentId}段录音失败:`, error);
                    if (isSegmentRecording) {
                        // 录音失败，短暂延迟后重试
                        setTimeout(() => {
                            if (isSegmentRecording) {
                                startNextSegmentAsync();
                            }
                        }, 1000);
                    }
                });
        };

        // 异步处理单段识别
        const processSegmentAsync = async (audioData, segmentId) => {
            try {
                console.log(`异步处理第${segmentId}段识别`);

                // 将识别任务加入待处理队列
                const recognitionPromise = recognizeSegment(audioData.audioBase64, audioData.audioFormat, segmentId);
                pendingRecognitions.set(segmentId, recognitionPromise);

                // 异步处理识别结果
                const hasContent = await recognitionPromise;

                // 从待处理队列中移除
                pendingRecognitions.delete(segmentId);

                console.log(`第${segmentId}段识别完成: hasContent=${hasContent}`);

                // 处理识别结果
                handleSegmentResult(hasContent, segmentId);

            } catch (error) {
                console.error(`第${segmentId}段异步识别失败:`, error);
                pendingRecognitions.delete(segmentId);

                // 识别失败不影响录音继续
                if (segmentId === 1) {
                    // 第一段识别失败，停止录音
                    stopSegmentedRecording(true);
                    VoiceUI.showVoiceStatus('语音识别服务异常，请重新尝试', 'error', 'conversationChat');
                }
            }
        };

        // 处理单段识别结果
        const handleSegmentResult = (hasContent, segmentId) => {
            if (hasContent === false) {
                // 没有识别到内容，记录并检查是否应该停止录音
                lastEmptySegmentId = segmentId;
                console.log(`第${segmentId}段无内容，记录为最后空段`);

                if (segmentId === 1) {
                    // 第一段就没有内容
                    stopSegmentedRecording(true);
                    VoiceUI.showVoiceStatus('未检测到语音内容，录音已停止。请重新点击麦克风按钮开始录音。', 'error', 'conversationChat');
                } else {
                    // 后续段没有内容，停止录音
                    stopSegmentedRecording(false);
                }
            } else if (hasContent === null) {
                // 识别异常
                console.log(`第${segmentId}段识别异常`);
                if (segmentId === 1) {
                    stopSegmentedRecording(true);
                    VoiceUI.showVoiceStatus('语音识别服务异常，请重新尝试', 'error', 'conversationChat');
                } else if (accumulatedText.trim().length > 0) {
                    // 后续段异常但有累积内容，正常结束
                    stopSegmentedRecording(false);
                }
            }
            // hasContent === true 时不需要特殊处理，录音会自动继续
        };



        // 录制单个10秒片段
        const startSingleSegmentRecording = () => {
            return new Promise(async (resolve, reject) => {
                try {
                    // 清空之前的录音数据
                    voiceManager.audioChunks = [];

                    // 开始录音
                    await voiceManager.startRecording();
                    console.log('开始10秒录音片段');

                    // 10秒后停止录音
                    currentSegmentTimeout = setTimeout(async () => {
                        try {
                            console.log('10秒录音时间到，停止录音');
                            voiceManager.stopRecording();

                            // 等待录音完成并获取数据
                            setTimeout(async () => {
                                try {
                                    // 获取录音数据
                                    const audioData = await voiceManager.getLastRecordingData();
                                    console.log('获取录音数据结果:', audioData ? '成功' : '失败');
                                    resolve(audioData);
                                } catch (error) {
                                    console.error('获取录音数据失败:', error);
                                    resolve(null);
                                }
                            }, 1000); // 增加等待时间确保录音数据准备好
                        } catch (error) {
                            console.error('停止录音失败:', error);
                            resolve(null);
                        }
                    }, 10000); // 10秒录音

                } catch (error) {
                    console.error('开始录音失败:', error);
                    reject(error);
                }
            });
        };

        // 识别单个语音片段
        const recognizeSegment = async (audioBase64, audioFormat, segmentIndex) => {
            try {
                console.log(`开始识别第${segmentIndex}段语音，格式: ${audioFormat}, 大小: ${audioBase64.length} 字符`);

                const response = await api.speechRecognition(
                    audioBase64,
                    audioFormat,
                    8000
                );

                console.log(`第${segmentIndex}段语音识别API响应:`, response);
                console.log(`第${segmentIndex}段响应详情 - success: ${response.success}, data:`, response.data);

                // 检查响应状态和数据
                console.log(`第${segmentIndex}段完整响应:`, JSON.stringify(response, null, 2));

                // 详细检查响应结构
                console.log(`第${segmentIndex}段响应类型检查:`, {
                    responseType: typeof response,
                    hasSuccess: 'success' in response,
                    successValue: response.success,
                    hasData: 'data' in response,
                    dataType: typeof response.data,
                    dataContent: response.data
                });

                // 检查是否是服务器错误（500状态码）
                if (response && response.error && response.error.includes('500')) {
                    console.error(`第${segmentIndex}段服务器错误:`, response.error);
                    return null; // 服务器错误返回null，区别于无内容的false
                }

                console.log(`第${segmentIndex}段开始处理响应，success字段:`, response.success, '类型:', typeof response.success);

                // 简化响应处理逻辑，只要不是明确的失败就尝试提取文本
                if (response && response.success !== false) {
                    console.log(`第${segmentIndex}段响应成功，开始提取文本`);
                    // 检查不同的响应格式
                    let recognizedText = '';

                    // 尝试多种可能的数据结构
                    if (response.data) {
                        console.log(`第${segmentIndex}段response.data存在:`, response.data, '类型:', typeof response.data);
                        if (typeof response.data === 'string') {
                            recognizedText = response.data.trim();
                            console.log(`第${segmentIndex}段从data字符串提取:`, recognizedText);
                        } else if (response.data.text) {
                            recognizedText = response.data.text.trim();
                            console.log(`第${segmentIndex}段从data.text提取:`, recognizedText);
                        } else if (response.data.result) {
                            recognizedText = response.data.result.trim();
                            console.log(`第${segmentIndex}段从data.result提取:`, recognizedText);
                        } else if (response.data.transcript) {
                            recognizedText = response.data.transcript.trim();
                            console.log(`第${segmentIndex}段从data.transcript提取:`, recognizedText);
                        }
                    } else if (response.text) {
                        recognizedText = response.text.trim();
                        console.log(`第${segmentIndex}段从response.text提取:`, recognizedText);
                    } else if (response.result) {
                        recognizedText = response.result.trim();
                        console.log(`第${segmentIndex}段从response.result提取:`, recognizedText);
                    } else if (typeof response === 'string') {
                        recognizedText = response.trim();
                        console.log(`第${segmentIndex}段从response字符串提取:`, recognizedText);
                    }

                    console.log(`第${segmentIndex}段最终提取的文本: "${recognizedText}"`);

                    if (recognizedText && recognizedText.length > 0) {
                        console.log(`第${segmentIndex}段识别成功: "${recognizedText}"`);
                        const oldAccumulatedText = accumulatedText;
                        accumulatedText += (accumulatedText ? ' ' : '') + recognizedText;
                        console.log(`累积文本更新: "${oldAccumulatedText}" -> "${accumulatedText}"`);
                        console.log(`当前累积文本长度: ${accumulatedText.length}`);
                        console.log(`返回true表示有内容`);
                        return true; // 有内容
                    } else {
                        console.log(`第${segmentIndex}段无有效语音内容`);
                        return false; // 无内容
                    }
                } else {
                    console.log(`第${segmentIndex}段识别失败:`, response ? (response.error || '未知错误') : '响应为空');
                    return false; // 识别失败视为无内容
                }
            } catch (error) {
                console.error(`第${segmentIndex}段识别错误:`, error);
                return null; // 网络错误等返回null
            }
        };





        // 停止分段录音
        const stopSegmentedRecording = (shouldHideModal = true, isManualStop = false) => {
            console.log('停止分段录音调用，参数:', {
                accumulatedText: accumulatedText,
                accumulatedTextLength: accumulatedText.length,
                accumulatedTextTrimmed: accumulatedText.trim(),
                accumulatedTextTrimmedLength: accumulatedText.trim().length,
                shouldHideModal: shouldHideModal,
                segmentCount: segmentCount
            });

            // 重置分段录音模式
            voiceManager.isSegmentRecording = false;
            isSegmentRecording = false;

            if (segmentTimer) {
                clearTimeout(segmentTimer);
                segmentTimer = null;
            }

            // 清理当前录音段的超时器
            if (currentSegmentTimeout) {
                clearTimeout(currentSegmentTimeout);
                currentSegmentTimeout = null;
            }

            // 等待所有异步识别任务完成
            if (pendingRecognitions.size > 0) {
                console.log(`等待${pendingRecognitions.size}个异步识别任务完成`);
                Promise.allSettled(Array.from(pendingRecognitions.values()))
                    .then(() => {
                        console.log('所有异步识别任务已完成');
                        finalizeStopping(shouldHideModal, isManualStop);
                    });
                return;
            }

            // 停止当前录音
            if (voiceManager.isRecording) {
                voiceManager.stopRecording();
            }

            VoiceUI.updateVoiceButtonState(button, false);

            // 只在需要时才隐藏聆听弹窗
            if (shouldHideModal) {
                hideListeningModal();
            }

            // 如果没有异步任务，直接完成停止
            finalizeStopping(shouldHideModal, isManualStop);
        };

        // 完成停止录音的最终处理
        const finalizeStopping = (shouldHideModal, isManualStop) => {
            console.log('完成停止录音的最终处理');

            // 处理累积的文本
            if (accumulatedText.trim().length > 0) {
                console.log('处理累积的语音识别结果:', accumulatedText.trim());
                // 隐藏聆听弹窗并显示加载动画
                hideListeningModal();
                showVoiceLoadingAnimation();
                processAccumulatedText(accumulatedText.trim());
            } else if (segmentCount > 0 && shouldHideModal && !isManualStop) {
                // 有录音但没有识别到内容，且需要隐藏弹窗时才显示错误（手动停止时不显示）
                console.log('录音完成但未识别到有效语音内容');
                VoiceUI.showVoiceStatus('录音完成，但未识别到语音内容', 'error', 'conversationChat');
            }

            // 重置状态
            accumulatedText = '';
            segmentCount = 0;
            pendingRecognitions.clear();
            lastEmptySegmentId = 0;
        };

        // 设置静音自动结束（备用机制）
        const resetSilenceTimer = () => {
            if (silenceTimer) {
                clearTimeout(silenceTimer);
            }
            silenceTimer = setTimeout(() => {
                if (isSegmentRecording) {
                    console.log('录音超时，自动停止');
                    stopSegmentedRecording();
                }
            }, 60000); // 60秒超时保护
        };

        // 检查是否有语音活动（保留变量以兼容其他代码）
        let lastSpeechTime = Date.now();

        // 显示语音识别加载动画
        const showVoiceLoadingAnimation = () => {
            const time = new Date().toLocaleTimeString('zh-CN', {
                hour: '2-digit',
                minute: '2-digit'
            });

            const loadingDiv = document.createElement('div');
            loadingDiv.id = 'voiceLoadingAnimation';
            loadingDiv.className = 'message user-message user-loading-indicator';
            loadingDiv.innerHTML = `
                <div class="message-wrapper">
                    <div class="message-content">
                        <div class="voice-loading-dots">
                            <span></span>
                            <span></span>
                            <span></span>
                        </div>
                        <div class="message-time">${time}</div>
                    </div>
                </div>
                <div class="message-avatar user-avatar" title="您">
                    <img src="${localStorage.getItem('userAvatar') || 'images/user.png'}" alt="用户头像" width="40" height="40">
                </div>
            `;

            const messagesContainer = document.getElementById('conversationMessages');
            messagesContainer.appendChild(loadingDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        };

        // 隐藏语音识别加载动画
        const hideVoiceLoadingAnimation = () => {
            const loadingDiv = document.getElementById('voiceLoadingAnimation');
            if (loadingDiv) {
                loadingDiv.remove();
            }
        };



        // 全局音频控制变量
        let currentAudio = null;
        let currentAudioUrl = null;
        let isAudioPaused = false;

        // 同步文字显示和音频播放
        const startSyncTextAndAudio = async (text, audioBase64) => {
            try {
                console.log('开始同步文字显示和音频播放');

                // 清理之前的音频
                if (currentAudio) {
                    currentAudio.pause();
                    if (currentAudioUrl) {
                        URL.revokeObjectURL(currentAudioUrl);
                    }
                }

                // 将base64音频转换为blob
                const audioBlob = base64ToBlob(audioBase64, 'audio/mp3');
                currentAudioUrl = URL.createObjectURL(audioBlob);
                currentAudio = new Audio(currentAudioUrl);

                // 显示音频控制按钮（包含暂停和停止按钮）
                showAudioControlButton();

                // 设置音频事件监听器
                currentAudio.onloadstart = () => {
                    console.log('音频开始加载');
                };

                currentAudio.oncanplay = () => {
                    console.log('音频可以播放');
                };

                currentAudio.onended = () => {
                    URL.revokeObjectURL(currentAudioUrl);
                    currentAudio = null;
                    currentAudioUrl = null;
                    isAudioPaused = false;
                    hideAudioControlButton();
                    console.log('音频播放完成，按钮已隐藏');
                };

                currentAudio.onerror = (error) => {
                    console.error('音频播放失败:', error);
                    URL.revokeObjectURL(currentAudioUrl);
                    currentAudio = null;
                    currentAudioUrl = null;
                    isAudioPaused = false;
                    hideAudioControlButton();
                    console.log('音频播放失败，按钮已隐藏');
                };

                // 同时开始文字显示和音频播放
                const textPromise = UIComponents.typeMessage('conversationMessages', text, false, 30);
                const audioPromise = currentAudio.play().catch(error => {
                    console.error('音频播放失败:', error);
                });

                // 等待两者都完成（或其中一个失败）
                await Promise.allSettled([textPromise, audioPromise]);

            } catch (error) {
                console.error('同步播放失败:', error);
                // 如果音频失败，至少显示文字
                await UIComponents.typeMessage('conversationMessages', text, false, 30);
            }
        };

        // 显示音频控制按钮
        const showAudioControlButton = () => {
            // 移除已存在的按钮
            const existingButton = document.getElementById('audioControlButton');
            if (existingButton) {
                existingButton.remove();
            }
            const existingStopButton = document.getElementById('stopAudioButton');
            if (existingStopButton) {
                existingStopButton.remove();
            }

            // 创建音频控制按钮
            const button = document.createElement('button');
            button.id = 'audioControlButton';
            button.innerHTML = '<i class="fas fa-pause"></i>';
            button.title = '暂停朗读';
            button.style.cssText = `
                position: fixed;
                top: 20px;
                right: 80px;
                width: 50px;
                height: 50px;
                border: none;
                border-radius: 50%;
                background: #3b82f6;
                color: white;
                cursor: pointer;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 18px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                z-index: 1000;
                transition: all 0.3s ease;
            `;

            // 添加悬停效果
            button.onmouseenter = () => {
                button.style.background = '#2563eb';
                button.style.transform = 'scale(1.1)';
            };
            button.onmouseleave = () => {
                button.style.background = '#3b82f6';
                button.style.transform = 'scale(1)';
            };

            // 添加点击事件
            button.onclick = toggleAudioPlayback;

            // 创建停止音频按钮
            const stopButton = document.createElement('button');
            stopButton.id = 'stopAudioButton';
            stopButton.innerHTML = '<i class="fas fa-stop"></i>';
            stopButton.title = '结束当前对话';
            stopButton.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                width: 50px;
                height: 50px;
                border: none;
                border-radius: 50%;
                background: #ef4444;
                color: white;
                cursor: pointer;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 18px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                z-index: 1000;
                transition: all 0.3s ease;
            `;

            // 添加悬停效果
            stopButton.onmouseenter = () => {
                stopButton.style.background = '#dc2626';
                stopButton.style.transform = 'scale(1.1)';
            };
            stopButton.onmouseleave = () => {
                stopButton.style.background = '#ef4444';
                stopButton.style.transform = 'scale(1)';
            };

            // 添加点击事件
            stopButton.onclick = stopCurrentConversation;

            // 添加到页面
            document.body.appendChild(button);
            document.body.appendChild(stopButton);
        };

        // 隐藏音频控制按钮
        const hideAudioControlButton = () => {
            const button = document.getElementById('audioControlButton');
            if (button) {
                button.remove();
            }
            const stopButton = document.getElementById('stopAudioButton');
            if (stopButton) {
                stopButton.remove();
            }
        };



        // 显示右上角错误弹窗
        const showErrorNotification = (message, duration = 5000) => {
            // 移除已存在的错误弹窗
            const existingError = document.getElementById('errorNotification');
            if (existingError) {
                existingError.remove();
            }

            // 创建错误弹窗
            const notification = document.createElement('div');
            notification.id = 'errorNotification';
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: #ef4444;
                color: white;
                padding: 16px 20px;
                border-radius: 8px;
                font-size: 14px;
                max-width: 300px;
                z-index: 1002;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
                display: flex;
                align-items: center;
                gap: 12px;
            `;

            // 添加错误图标
            const icon = document.createElement('i');
            icon.className = 'fas fa-exclamation-triangle';
            icon.style.fontSize = '16px';
            notification.appendChild(icon);

            // 添加错误文本
            const text = document.createElement('span');
            text.textContent = message;
            notification.appendChild(text);

            // 添加关闭按钮
            const closeBtn = document.createElement('button');
            closeBtn.innerHTML = '<i class="fas fa-times"></i>';
            closeBtn.style.cssText = `
                background: none;
                border: none;
                color: white;
                cursor: pointer;
                padding: 0;
                margin-left: auto;
                font-size: 14px;
                opacity: 0.8;
                transition: opacity 0.2s ease;
            `;
            closeBtn.onmouseenter = () => closeBtn.style.opacity = '1';
            closeBtn.onmouseleave = () => closeBtn.style.opacity = '0.8';
            closeBtn.onclick = () => notification.remove();
            notification.appendChild(closeBtn);

            // 添加到页面
            document.body.appendChild(notification);

            // 自动消失
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.style.opacity = '0';
                    notification.style.transform = 'translateX(100%)';
                    setTimeout(() => {
                        if (notification.parentNode) {
                            notification.remove();
                        }
                    }, 300);
                }
            }, duration);

            console.log('显示错误弹窗:', message);
        };

        // 切换音频播放/暂停
        const toggleAudioPlayback = () => {
            if (!currentAudio) return;

            const button = document.getElementById('audioControlButton');
            if (!button) return;

            if (isAudioPaused) {
                // 继续播放
                currentAudio.play().then(() => {
                    isAudioPaused = false;
                    button.innerHTML = '<i class="fas fa-pause"></i>';
                    button.title = '暂停朗读';
                    console.log('音频继续播放');
                }).catch(error => {
                    console.error('音频继续播放失败:', error);
                });
            } else {
                // 暂停播放
                currentAudio.pause();
                isAudioPaused = true;
                button.innerHTML = '<i class="fas fa-play"></i>';
                button.title = '继续朗读';
                console.log('音频已暂停');
            }
        };

        // 停止当前对话
        const stopCurrentConversation = () => {
            console.log('=== 用户请求停止当前对话 ===');

            // 停止当前音频播放
            if (currentAudio) {
                currentAudio.pause();
                currentAudio.currentTime = 0; // 重置播放位置
                if (currentAudioUrl) {
                    URL.revokeObjectURL(currentAudioUrl);
                }
                currentAudio = null;
                currentAudioUrl = null;
                isAudioPaused = false;
                console.log('已停止音频播放');
            }

            // 隐藏控制按钮
            hideAudioControlButton();

            // 停止任何正在进行的录音
            if (isSegmentRecording) {
                console.log('停止正在进行的录音');
                isSegmentRecording = false;
                voiceManager.isSegmentRecording = false;

                // 清除所有定时器
                if (currentSegmentTimeout) {
                    clearTimeout(currentSegmentTimeout);
                    currentSegmentTimeout = null;
                }
                if (segmentTimer) {
                    clearTimeout(segmentTimer);
                    segmentTimer = null;
                }

                // 停止录音
                if (voiceManager.isRecording) {
                    voiceManager.stopRecording();
                }

                // 更新UI
                const voiceButton = document.querySelector('.voice-button');
                if (voiceButton) {
                    VoiceUI.updateVoiceButtonState(voiceButton, false);
                }
                hideListeningModal();
                hideVoiceLoadingAnimation();
            }

            // 清除延迟处理标志
            if (window.pendingManualStopProcessing) {
                window.pendingManualStopProcessing = false;
                console.log('清除延迟处理标志');
            }

            // 重置累积状态
            accumulatedText = '';
            segmentCount = 0;

            // 隐藏机器人打字指示器
            UIComponents.hideBotTypingIndicator('conversationMessages');

            console.log('当前对话已完全停止，可以开始新对话');

            // 可选：显示提示信息
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 80px;
                right: 20px;
                background: #10b981;
                color: white;
                padding: 12px 20px;
                border-radius: 8px;
                font-size: 14px;
                z-index: 1001;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
            `;
            notification.textContent = '对话已结束，可以开始新对话';
            document.body.appendChild(notification);

            // 3秒后自动移除提示
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 3000);
        };

        // 添加键盘快捷键支持
        document.addEventListener('keydown', (event) => {
            // 防止在输入框中触发
            const activeElement = document.activeElement;
            const isInInputField = activeElement && (activeElement.tagName === 'INPUT' || activeElement.tagName === 'TEXTAREA');

            // 空格键控制音频播放/暂停
            if (event.code === 'Space' && currentAudio && !isInInputField) {
                event.preventDefault();
                toggleAudioPlayback();
            }

            // Escape键停止当前对话
            if (event.code === 'Escape') {
                // Escape键在任何情况下都可以使用
                event.preventDefault();
                stopCurrentConversation();
            }
        });



        // base64转blob的辅助函数
        const base64ToBlob = (base64, mimeType) => {
            const byteCharacters = atob(base64);
            const byteNumbers = new Array(byteCharacters.length);
            for (let i = 0; i < byteCharacters.length; i++) {
                byteNumbers[i] = byteCharacters.charCodeAt(i);
            }
            const byteArray = new Uint8Array(byteNumbers);
            return new Blob([byteArray], { type: mimeType });
        };

        // 检查是否需要处理延迟的手动停止
        const checkPendingManualStopProcessing = () => {
            if (window.pendingManualStopProcessing) {
                console.log('检测到延迟处理标志，当前累积文本:', accumulatedText);

                // 清除标志
                window.pendingManualStopProcessing = false;

                // 如果有累积文本，处理它
                if (accumulatedText.trim().length > 0) {
                    console.log('处理延迟获得的累积文本:', accumulatedText);
                    showVoiceLoadingAnimation();
                    processAccumulatedText(accumulatedText.trim());

                    // 重置状态
                    accumulatedText = '';
                    segmentCount = 0;
                } else {
                    console.log('延迟处理时无累积文本');
                    // 即使没有文本也要重置状态
                    accumulatedText = '';
                    segmentCount = 0;
                }
            }
        };

        // 处理累积的识别文本
        const processAccumulatedText = async (text) => {
            try {
                console.log('=== 开始处理累积的识别文本 ===');
                console.log('累积文本内容:', text);
                console.log('文本长度:', text.length);



                // 隐藏语音录音加载动画
                hideVoiceLoadingAnimation();

                // 立即显示完整用户消息
                UIComponents.addMessage('conversationMessages', text, true);

                // 显示机器人打字指示器
                UIComponents.showBotTypingIndicator('conversationMessages');

                // 调用对话API获取回复（包含音频）
                const sessionId = sessionManager.getSessionId(moduleType);
                const response = await api.conversationChat(text, sessionId, true, 'longxiaochun_v2');

                // 隐藏机器人打字指示器
                UIComponents.hideBotTypingIndicator('conversationMessages');

                if (response.success) {
                    const data = response.data;

                    // 检查不同的响应数据结构
                    let responseText = null;
                    if (data.message) {
                        responseText = data.message;
                    } else if (data.response) {
                        responseText = data.response;
                    } else if (data.text_response) {
                        responseText = data.text_response;
                    } else if (typeof data === 'string') {
                        responseText = data;
                    }

                    // 流式显示AI回复并同时朗读
                    if (responseText) {
                        console.log('=== 开始AI回复显示和朗读 ===');
                        console.log('回复文本:', responseText.substring(0, 100) + '...');

                        // 检查是否有音频数据
                        const audioData = data.audio_data;
                        if (audioData) {
                            console.log('检测到音频数据，开始同步播放');
                            // 同时开始文字显示和音频播放
                            startSyncTextAndAudio(responseText, audioData);
                        } else {
                            console.log('无音频数据，仅显示文字');
                            // 仅显示文字
                            await UIComponents.typeMessage('conversationMessages', responseText, false, 30);
                        }
                        console.log('AI回复显示完成');
                    } else {
                        console.error('未找到有效的回复内容:', data);
                        const errorMessage = '抱歉，我暂时无法回复您的问题。';
                        // 错误消息也需要音频控制，但没有音频数据，只显示文字
                        UIComponents.addMessage('conversationMessages', errorMessage, false);
                    }
                } else {
                    throw new Error(response.error || '对话失败');
                }

            } catch (error) {
                console.error('处理累积文本失败:', error);
                hideVoiceLoadingAnimation();
                UIComponents.hideBotTypingIndicator('conversationMessages');
                // 不显示错误提示，静默处理
            }
        };

        // 禁用语音管理器的音频处理回调（分段录音模式下不使用）
        voiceManager.onAudioReady = () => {
            // 分段录音模式下不使用此回调
            console.log('分段录音模式下忽略onAudioReady回调');
        };

        // 测试API调用的函数
        window.testSpeechAPI = async () => {
            console.log('=== 测试语音识别API ===');
            try {
                const testResponse = await api.speechRecognition('dGVzdA==', 'wav', 16000);
                console.log('测试API响应:', testResponse);
                console.log('响应类型:', typeof testResponse);
                console.log('success字段:', testResponse.success);
                console.log('data字段:', testResponse.data);
            } catch (error) {
                console.error('测试API调用失败:', error);
            }
        };

        // 测试TTS API的函数
        window.testTTSAPI = async () => {
            console.log('=== 测试TTS API ===');
            try {
                const testText = '你好，这是一个测试。';
                console.log('测试文本:', testText);
                const testResponse = await api.textToSpeech(testText, 'longxiaochun_v2', 'mp3');
                console.log('TTS API响应:', testResponse);
                console.log('响应类型:', typeof testResponse);
                console.log('success字段:', testResponse.success);
                console.log('data字段:', testResponse.data);

                const testAudioData = testResponse.data && (testResponse.data.audio_base64 || testResponse.data.audio_data);
                if (testResponse.success && testAudioData) {
                    console.log('TTS成功，使用音频控制播放');
                    // 使用带控制的播放方式
                    startSyncTextAndAudio('你好，这是一个测试。', testAudioData);
                }
            } catch (error) {
                console.error('测试TTS API调用失败:', error);
            }
        };

        // 按钮点击事件
        button.addEventListener('click', async () => {
            console.log('=== 麦克风按钮点击 ===');
            console.log('当前状态:', { isSegmentRecording, voiceManagerIsRecording: voiceManager.isRecording });

            if (!isSegmentRecording) {
                // 开始分段录音
                try {
                    console.log('开始分段录音');
                    // 重置状态
                    accumulatedText = '';
                    segmentCount = 0;
                    isSegmentRecording = true;
                    voiceManager.isSegmentRecording = true;

                    // 初始化音频权限
                    await voiceManager.initializeAudio();

                    // 更新UI
                    VoiceUI.updateVoiceButtonState(button, true);
                    showListeningModal();

                    // 开始分段录音循环
                    startSegmentedRecording();

                    // 开始超时保护
                    resetSilenceTimer();

                    console.log('分段录音已开始');
                } catch (error) {
                    console.error('开始录音失败:', error);
                    showErrorNotification('无法开始录音: ' + error.message);
                    hideListeningModal();
                    isSegmentRecording = false;
                    voiceManager.isSegmentRecording = false;
                }
            } else {
                // 手动停止分段录音 - 简化逻辑
                console.log('手动停止分段录音');

                // 立即设置停止状态
                isSegmentRecording = false;
                voiceManager.isSegmentRecording = false;

                // 清除所有定时器
                if (currentSegmentTimeout) {
                    clearTimeout(currentSegmentTimeout);
                    currentSegmentTimeout = null;
                }
                if (segmentTimer) {
                    clearTimeout(segmentTimer);
                    segmentTimer = null;
                }

                // 更新UI
                VoiceUI.updateVoiceButtonState(button, false);
                hideListeningModal();

                // 设置一个标志，表示需要延迟处理
                window.pendingManualStopProcessing = true;
                console.log('设置延迟处理标志，等待识别完成...');

                // 处理当前录音片段（如果正在录音）
                if (voiceManager.isRecording) {
                    console.log('=== 手动停止时正在录音，需要处理当前片段 ===');
                    console.log('当前状态:', {
                        isRecording: voiceManager.isRecording,
                        segmentCount: segmentCount,
                        accumulatedText: accumulatedText,
                        audioChunksLength: voiceManager.audioChunks ? voiceManager.audioChunks.length : 'undefined'
                    });

                    // 停止录音
                    voiceManager.stopRecording();
                    console.log('已调用stopRecording()');

                    // 等待录音数据准备好，然后处理
                    setTimeout(async () => {
                        try {
                            console.log('开始获取录音数据...');
                            console.log('当前audioChunks状态:', {
                                length: voiceManager.audioChunks ? voiceManager.audioChunks.length : 'undefined',
                                totalSize: voiceManager.audioChunks ? voiceManager.audioChunks.reduce((sum, chunk) => sum + chunk.size, 0) : 0
                            });

                            // 获取当前录音片段的数据
                            const audioData = await voiceManager.getLastRecordingData();
                            console.log('手动停止时获取录音数据:', audioData ? '成功' : '失败');

                            if (audioData) {
                                console.log('录音数据详情:', {
                                    audioBase64Length: audioData.audioBase64.length,
                                    audioFormat: audioData.audioFormat
                                });

                                // 识别当前片段
                                segmentCount++;
                                console.log(`=== 手动停止时处理第${segmentCount}段录音 ===`);

                                const hasContent = await recognizeSegment(
                                    audioData.audioBase64,
                                    audioData.audioFormat,
                                    segmentCount
                                );

                                console.log(`手动停止时第${segmentCount}段识别结果:`, hasContent);
                                console.log(`手动停止时识别后的累积文本:`, accumulatedText);
                            } else {
                                console.error('手动停止时未获取到录音数据');
                                console.error('可能原因: audioChunks为空或数据太小');
                            }

                            // 检查是否需要处理延迟的手动停止
                            console.log('手动停止时获取到当前片段，检查是否需要处理');
                            checkPendingManualStopProcessing();

                        } catch (error) {
                            console.error('手动停止时处理录音数据失败:', error);

                            // 出错时也检查延迟处理
                            console.log('手动停止时出错，检查是否需要处理');
                            checkPendingManualStopProcessing();
                        }
                    }, 1000); // 等待1秒确保录音数据准备好
                } else {
                    console.log('手动停止时未在录音，检查是否需要处理');
                    checkPendingManualStopProcessing();
                }
            }
        });
    }
}

// 全局函数：切换密码显示
function togglePasswordVisibility(inputId) {
    const input = document.getElementById(inputId);
    const icon = input.parentElement.querySelector('.toggle-password');

    if (input.type === 'password') {
        input.type = 'text';
        icon.className = 'fas fa-eye toggle-password';
        icon.title = '隐藏密码';
    } else {
        input.type = 'password';
        icon.className = 'fas fa-eye-slash toggle-password';
        icon.title = '显示密码';
    }
}

// 全局函数：重置头像
function resetAvatar() {
    const currentUserAvatar = document.getElementById('currentUserAvatar');
    if (currentUserAvatar) {
        currentUserAvatar.src = 'images/user.png';
        localStorage.removeItem('userAvatar');
    }
}



// 全局函数：保存设置
async function saveSettings() {
    const apiKey = document.getElementById('apiKey').value.trim();

    const formData = { apiKey };
    const validation = UIComponents.validateForm(formData);

    if (!validation.isValid) {
        ErrorHandler.show(validation.errors.join('\n'));
        return;
    }

    try {
        LoadingManager.show('保存设置中...');

        // 保存到本地存储
        UIComponents.saveSettings(formData);

        // 更新后端配置
        if (apiKey) {
            await api.updateConfig(apiKey);
        }

        UIComponents.hideModal('settingsModal');
        ErrorHandler.showSuccess('设置保存成功');

    } catch (error) {
        console.error('保存设置失败:', error);
        ErrorHandler.show('保存设置失败: ' + error.message);
    } finally {
        LoadingManager.hide();
    }
}

// 应用启动
document.addEventListener('DOMContentLoaded', () => {
    window.app = new LanguageMentorApp();
});
