/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    background: #f7f7f8;
    color: #1f2937;
    height: 100vh;
    overflow: hidden;
}

.app-container {
    height: 100vh;
    display: flex;
    overflow: hidden;
}

/* 侧边栏 */
.sidebar {
    width: 200px;
    background: #202123;
    display: flex;
    flex-direction: column;
    height: 100vh;
    border-right: 1px solid #4d4d4f;
}

.sidebar-header {
    padding: 1rem;
    border-bottom: 1px solid #4d4d4f;
}

.app-logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    color: #ffffff;
    font-size: 1.1rem;
    font-weight: 600;
}

.app-logo i {
    color: #10a37f;
    font-size: 1.3rem;
}

.sidebar-nav {
    flex: 1;
    padding: 1rem 0;
    overflow-y: auto;
}

.nav-item {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    margin: 0.25rem 0.5rem;
    border: none;
    background: transparent;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.9rem;
    color: #ececf1;
    text-align: left;
    border-radius: 6px;
    width: calc(100% - 1rem);
}

.nav-item i {
    margin-right: 0.75rem;
    font-size: 1rem;
    width: 20px;
    text-align: center;
    opacity: 0.7;
}

.nav-item:hover {
    background: #2a2b32;
}

.nav-item.active {
    background: #10a37f;
    color: #ffffff;
}

.nav-item.active i {
    opacity: 1;
}

.sidebar-footer {
    padding: 1rem;
    border-top: 1px solid #4d4d4f;
}

.settings-btn {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    border: none;
    background: transparent;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.9rem;
    color: #ececf1;
    text-align: left;
    border-radius: 6px;
    width: 100%;
}

.settings-btn i {
    margin-right: 0.75rem;
    font-size: 1rem;
    width: 20px;
    text-align: center;
    opacity: 0.7;
}

.settings-btn:hover {
    background: #2a2b32;
}

/* 主要内容区域 */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    height: 100vh;
    background: #ffffff;
    overflow: hidden;
}

/* 内容区域 */
.content-area {
    flex: 1;
    overflow-y: auto;
    height: 100%;
}

/* 标签页内容 */
.tab-content {
    display: none;
    height: 100%;
    width: 100%;
    overflow-y: auto;
    flex-direction: column;
}

.tab-content.active {
    display: flex;
}

.tab-header {
    padding: 1.2rem 2rem;
    border-bottom: 1px solid #f0f0f0;
    background: #ffffff;
}

.tab-header h2 {
    font-size: 1.5rem;
    color: #202123;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.tab-header p {
    color: #6e6e80;
    font-size: 0.95rem;
    margin: 0;
}

/* 按钮样式 */
.btn {
    padding: 0.75rem 1.25rem;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.95rem;
    font-weight: 500;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
}

.btn-primary {
    background: #10a37f;
    color: white;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.btn-primary:hover {
    background: #0e8f6f;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
}

.btn-secondary {
    background: #f7f7f8;
    color: #353740;
    border: 1px solid #d9d9e3;
}

.btn-secondary:hover {
    background: #ececf1;
}

.btn-outline {
    background: transparent;
    color: #353740;
    border: 1px solid #d9d9e3;
}

.btn-outline:hover {
    background: #f7f7f8;
    border-color: #10a37f;
    color: #10a37f;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .app-container {
        flex-direction: column;
    }

    .sidebar {
        width: 100%;
        height: auto;
        border-right: none;
        border-bottom: 1px solid #4d4d4f;
    }

    .sidebar-header {
        padding: 0.75rem 1rem;
    }

    .app-logo {
        font-size: 1rem;
    }

    .sidebar-nav {
        flex-direction: row;
        overflow-x: auto;
        padding: 0.5rem;
    }

    .nav-item {
        white-space: nowrap;
        min-width: 120px;
        justify-content: center;
        padding: 0.75rem 1rem;
        margin: 0 0.25rem;
    }

    .sidebar-footer {
        display: none;
    }

    .main-content {
        height: calc(100vh - 120px);
    }

    .tab-header {
        padding: 1rem 1.5rem;
    }

    .tab-header h2 {
        font-size: 1.3rem;
    }
}

/* 加载动画 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.loading-overlay.show {
    display: flex;
}

.loading-spinner {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    text-align: center;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}

.loading-spinner i {
    font-size: 2rem;
    color: #667eea;
    margin-bottom: 1rem;
}

.loading-spinner p {
    color: #64748b;
    font-weight: 500;
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #cbd5e0;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a0aec0;
}

/* 案例检索样式 */
.case-search-intro {
    margin: 0 24px 15px;
    padding: 1.5rem;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-radius: 12px;
    border-left: 4px solid #1e3a8a;
}

.search-tips h3 {
    color: #1e3a8a;
    margin-bottom: 1rem;
    font-size: 1.2rem;
}

.search-tips p {
    color: #4a5568;
    margin-bottom: 1rem;
    line-height: 1.6;
}

.search-tips ul {
    margin-bottom: 1.5rem;
    padding-left: 1.5rem;
}

.search-tips li {
    color: #4a5568;
    margin-bottom: 0.5rem;
    line-height: 1.5;
}

.common-crimes h4 {
    color: #2d3748;
    margin-bottom: 1rem;
    font-size: 1rem;
}

.crime-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.crime-tag {
    padding: 0.5rem 1rem;
    background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 100%);
    color: white;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    user-select: none;
}

.crime-tag:hover {
    background: linear-gradient(135deg, #1e40af 0%, #2563eb 100%);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(30, 58, 138, 0.3);
}

/* 响应式设计补充 */
@media (max-width: 768px) {
    .crime-tags {
        gap: 0.25rem;
    }
    
    .crime-tag {
        padding: 0.4rem 0.8rem;
        font-size: 0.8rem;
    }
    
    .case-search-intro {
        padding: 1rem;
    }
}
