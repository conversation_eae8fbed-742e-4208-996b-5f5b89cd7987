// UI 组件管理类
class UIComponents {
    // 创建消息元素
    static createMessage(content, isUser = false, timestamp = null, containerId = null) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${isUser ? 'user' : 'bot'}`;

        const time = timestamp || new Date().toLocaleTimeString('zh-CN', {
            hour: '2-digit',
            minute: '2-digit'
        });

        // 创建头像
        let avatarSrc;
        let avatarTitle;
        let avatarClass;
        if (isUser) {
            avatarSrc = localStorage.getItem('userAvatar') || 'images/user.png';
            avatarTitle = '用户';
            avatarClass = 'user-avatar';
        } else {
            // 所有智能体都使用相同的robot头像
            avatarSrc = 'images/robot.png';
            avatarTitle = '法律助手';
            avatarClass = 'bot-avatar';
        }

        const imgTag = avatarSrc ? `<img src="${avatarSrc}" alt="${avatarTitle}" width="40" height="40">` : '';

        messageDiv.innerHTML = `
            <div class="message-avatar ${avatarClass}" title="${avatarTitle}">
                ${imgTag}
            </div>
            <div class="message-wrapper">
                <div class="message-content">
                    ${content}
                </div>
                <div class="message-time">${time}</div>
            </div>
        `;

        return messageDiv;
    }

    // 添加消息到聊天容器
    static addMessage(containerId, content, isUser = false) {
        const container = document.getElementById(containerId);
        if (!container) return;

        // 移除欢迎消息
        const welcomeMessage = container.querySelector('.welcome-message');
        if (welcomeMessage) {
            welcomeMessage.remove();
        }

        const messageElement = this.createMessage(content, isUser, null, containerId);
        container.appendChild(messageElement);

        // 滚动到底部
        container.scrollTop = container.scrollHeight;

        return messageElement;
    }

    // 添加用户消息并显示加载动画
    static addUserMessageWithLoading(containerId, content) {
        const container = document.getElementById(containerId);
        if (!container) return null;

        // 移除欢迎消息
        const welcomeMessage = container.querySelector('.welcome-message');
        if (welcomeMessage) {
            welcomeMessage.remove();
        }

        // 创建用户消息元素
        const messageElement = this.createMessage(content, true, null, containerId);
        container.appendChild(messageElement);

        // 添加用户消息加载动画
        const loadingElement = this.createUserLoadingIndicator(containerId);
        container.appendChild(loadingElement);

        // 滚动到底部
        container.scrollTop = container.scrollHeight;

        return { messageElement, loadingElement };
    }

    // 创建用户消息加载指示器
    static createUserLoadingIndicator(containerId) {
        const time = new Date().toLocaleTimeString('zh-CN', {
            hour: '2-digit',
            minute: '2-digit'
        });

        const loadingDiv = document.createElement('div');
        loadingDiv.className = 'message user-message user-loading-indicator';
        loadingDiv.id = 'userLoadingIndicator';

        loadingDiv.innerHTML = `
            <div class="message-wrapper">
                <div class="message-content">
                    <div class="user-loading-dots">
                        <span></span>
                        <span></span>
                        <span></span>
                    </div>
                    <div class="message-time">${time}</div>
                </div>
            </div>
            <div class="message-avatar user-avatar" title="您">
                <img src="${localStorage.getItem('userAvatar') || 'images/user.png'}" alt="用户头像" width="40" height="40">
            </div>
        `;

        return loadingDiv;
    }

    // 移除用户加载指示器
    static removeUserLoadingIndicator(containerId) {
        const container = document.getElementById(containerId);
        if (!container) return;

        const loadingIndicator = container.querySelector('#userLoadingIndicator');
        if (loadingIndicator) {
            loadingIndicator.remove();
        }
    }

    // 清空聊天消息
    static clearMessages(containerId) {
        const container = document.getElementById(containerId);
        if (!container) return;
        
        container.innerHTML = '';
    }

    // 显示欢迎消息
    static showWelcomeMessage(containerId, message) {
        const container = document.getElementById(containerId);
        if (!container) return;
        
        this.clearMessages(containerId);
        
        const welcomeDiv = document.createElement('div');
        welcomeDiv.className = 'welcome-message';
        welcomeDiv.innerHTML = message;
        
        container.appendChild(welcomeDiv);
    }

    // 设置输入框状态
    static setInputState(inputId, sendBtnId, enabled = true) {
        const input = document.getElementById(inputId);
        const sendBtn = document.getElementById(sendBtnId);

        if (input) {
            input.disabled = !enabled;
            if (enabled) {
                input.focus();
            }
        }

        if (sendBtn) {
            sendBtn.disabled = !enabled;
        }
    }

    // 显示打字指示器
    static showTypingIndicator(indicatorId) {
        const indicator = document.getElementById(indicatorId);
        if (indicator) {
            indicator.style.display = 'block';

            // 滚动到底部
            const container = indicator.closest('.chat-messages');
            if (container) {
                container.scrollTop = container.scrollHeight;
            }
        }
    }

    // 隐藏打字指示器
    static hideTypingIndicator(indicatorId) {
        const indicator = document.getElementById(indicatorId);
        if (indicator) {
            indicator.style.display = 'none';
        }
    }

    // 显示通用的机器人打字指示器
    static showBotTypingIndicator(containerId) {
        const container = document.getElementById(containerId);
        if (!container) return null;

        // 移除已存在的打字指示器
        const existingIndicator = container.querySelector('.bot-typing-indicator');
        if (existingIndicator) {
            existingIndicator.remove();
        }

        const time = new Date().toLocaleTimeString('zh-CN', {
            hour: '2-digit',
            minute: '2-digit'
        });

        const typingDiv = document.createElement('div');
        typingDiv.className = 'message bot-message bot-typing-indicator';
        typingDiv.innerHTML = `
            <div class="message-avatar bot-avatar" title="法律助手">
                <img src="images/robot.png" alt="法律助手" width="40" height="40">
            </div>
            <div class="message-wrapper">
                <div class="message-content">
                    <div class="typing-dots">
                        <span></span>
                        <span></span>
                        <span></span>
                    </div>
                    <div class="message-time">${time}</div>
                </div>
            </div>
        `;

        container.appendChild(typingDiv);
        container.scrollTop = container.scrollHeight;

        return typingDiv;
    }

    // 隐藏通用的机器人打字指示器
    static hideBotTypingIndicator(containerId) {
        const container = document.getElementById(containerId);
        if (!container) return;

        const indicator = container.querySelector('.bot-typing-indicator');
        if (indicator) {
            indicator.remove();
        }
    }

    // 切换标签页
    static switchTab(tabId) {
        // 隐藏所有标签页内容
        document.querySelectorAll('.tab-content').forEach(tab => {
            tab.classList.remove('active');
        });
        
        // 移除所有导航项的激活状态
        document.querySelectorAll('.nav-item').forEach(btn => {
            btn.classList.remove('active');
        });

        // 显示目标标签页
        const targetTab = document.getElementById(`${tabId}-tab`);
        if (targetTab) {
            targetTab.classList.add('active');
        }

        // 激活对应的导航项
        const targetBtn = document.querySelector(`[data-tab="${tabId}"]`);
        if (targetBtn) {
            targetBtn.classList.add('active');
        }
    }

    // 显示/隐藏模态框
    static showModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.classList.add('show');
        }
    }

    static hideModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.classList.remove('show');
        }
    }

    // 更新场景介绍
    static updateScenarioIntro(content) {
        const introElement = document.getElementById('scenarioIntro');
        if (introElement) {
            // 将 Markdown 转换为 HTML（简单处理）
            const htmlContent = this.markdownToHtml(content);
            introElement.innerHTML = htmlContent;
        }
    }

    // 更新律师推荐介绍
    static updateLawyerRecommendationIntro(content) {
        const introElement = document.getElementById('lawyerRecommendationIntro');
        const timeElement = document.getElementById('lawyerRecommendationIntroTime');

        if (introElement) {
            const htmlContent = this.markdownToHtml(content);
            introElement.innerHTML = htmlContent;
        }

        if (timeElement) {
            const time = new Date().toLocaleTimeString('zh-CN', {
                hour: '2-digit',
                minute: '2-digit'
            });
            timeElement.textContent = time;
        }
    }

    // 简单的 Markdown 转 HTML
    static markdownToHtml(markdown) {
        if (!markdown) return '';
        
        return markdown
            // 标题
            .replace(/^### (.*$)/gim, '<h3>$1</h3>')
            .replace(/^## (.*$)/gim, '<h2>$1</h2>')
            .replace(/^# (.*$)/gim, '<h1>$1</h1>')
            // 粗体
            .replace(/\*\*(.*)\*\*/gim, '<strong>$1</strong>')
            // 斜体
            .replace(/\*(.*)\*/gim, '<em>$1</em>')
            // 代码块
            .replace(/```([\s\S]*?)```/gim, '<pre><code>$1</code></pre>')
            // 行内代码
            .replace(/`([^`]*)`/gim, '<code>$1</code>')
            // 链接
            .replace(/\[([^\]]*)\]\(([^\)]*)\)/gim, '<a href="$2" target="_blank">$1</a>')
            // 换行
            .replace(/\n/gim, '<br>');
    }

    // 获取选中的场景
    static getSelectedScenario() {
        const selectedRadio = document.querySelector('input[name="scenario"]:checked');
        return selectedRadio ? selectedRadio.value : null;
    }

    // 设置场景选择
    static setSelectedScenario(scenarioId) {
        const radio = document.querySelector(`input[name="scenario"][value="${scenarioId}"]`);
        if (radio) {
            radio.checked = true;
        }
    }

    // 禁用/启用场景选择
    static setScenarioSelectionState(enabled = true) {
        const radios = document.querySelectorAll('input[name="scenario"]');
        radios.forEach(radio => {
            radio.disabled = !enabled;
        });
    }

    // 显示打字效果
    static async typeMessage(containerId, content, isUser = false, speed = 50) {
        const container = document.getElementById(containerId);
        if (!container) return;

        // 移除欢迎消息
        const welcomeMessage = container.querySelector('.welcome-message');
        if (welcomeMessage) {
            welcomeMessage.remove();
        }

        const messageElement = this.createMessage('', isUser, null, containerId);
        const contentElement = messageElement.querySelector('.message-content');
        container.appendChild(messageElement);

        // markdown转html
        content = DOMPurify.sanitize(marked.parse(content));

        // 打字效果
        let i = 0;
        const typeInterval = setInterval(() => {
            if (i < content.length) {
                const currentText = content.substring(0, i + 1);
                contentElement.innerHTML =`
                    ${currentText}
                `;
                i++;
                container.scrollTop = container.scrollHeight;
            } else {
                clearInterval(typeInterval);
            }
        }, speed);

        return messageElement;
    }

    // 验证表单
    static validateForm(formData) {
        const errors = [];
        
        if (formData.apiKey && formData.apiKey.length < 15) {
            errors.push('千问 API Key 长度不能少于15个字符');
        }
        
        return {
            isValid: errors.length === 0,
            errors
        };
    }

    // 保存设置到本地存储
    static saveSettings(settings) {
        try {
            localStorage.setItem('languageMentorSettings', JSON.stringify(settings));
            return true;
        } catch (error) {
            console.error('保存设置失败:', error);
            return false;
        }
    }

    // 从本地存储加载设置
    static loadSettings() {
        try {
            const settings = localStorage.getItem('languageMentorSettings');
            return settings ? JSON.parse(settings) : {};
        } catch (error) {
            console.error('加载设置失败:', error);
            return {};
        }
    }

    // 应用设置到表单
    static applySettingsToForm() {
        const settings = this.loadSettings();
        
        const apiKeyInput = document.getElementById('apiKey');
        
        if (apiKeyInput && settings.apiKey) {
            apiKeyInput.value = settings.apiKey;
        }
    }

    // 格式化时间戳
    static formatTimestamp(timestamp) {
        const date = new Date(timestamp);
        return date.toLocaleTimeString('zh-CN', {
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
    }

    // 检测移动设备
    static isMobile() {
        return window.innerWidth <= 768;
    }

    // 自适应布局调整
    static adjustLayoutForMobile() {
        if (this.isMobile()) {
            document.body.classList.add('mobile');
        } else {
            document.body.classList.remove('mobile');
        }
    }
}

// 键盘快捷键管理
class KeyboardManager {
    static init() {
        document.addEventListener('keydown', (e) => {
            // Ctrl/Cmd + Enter 发送消息
            if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
                const activeTab = document.querySelector('.tab-content.active');
                if (activeTab) {
                    const sendBtn = activeTab.querySelector('.send-btn');
                    if (sendBtn && !sendBtn.disabled) {
                        sendBtn.click();
                    }
                }
            }
            
            // Escape 关闭模态框
            if (e.key === 'Escape') {
                const openModal = document.querySelector('.modal.show');
                if (openModal) {
                    openModal.classList.remove('show');
                }
            }
        });
    }
}

// 初始化键盘管理
document.addEventListener('DOMContentLoaded', () => {
    KeyboardManager.init();
    UIComponents.adjustLayoutForMobile();
    
    // 监听窗口大小变化
    window.addEventListener('resize', () => {
        UIComponents.adjustLayoutForMobile();
    });
});
